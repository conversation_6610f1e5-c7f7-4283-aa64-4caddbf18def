import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DOCUMENT } from '@angular/common';
import { DeleteAccountComponent } from './delete-account.component';

describe('DeleteAccountComponent', () => {
  let component: DeleteAccountComponent;
  let fixture: ComponentFixture<DeleteAccountComponent>;
  let mockDocument: any;

  beforeEach(async () => {
    // Create a mock document
    mockDocument = {
      documentElement: {
        getAttribute: jasmine.createSpy('getAttribute').and.returnValue('ltr'),
        lang: 'en'
      },
      body: {
        getAttribute: jasmine.createSpy('getAttribute').and.returnValue('ltr')
      }
    };

    // Mock window.getComputedStyle
    spyOn(window, 'getComputedStyle').and.returnValue({
      direction: 'ltr'
    } as CSSStyleDeclaration);

    await TestBed.configureTestingModule({
      imports: [DeleteAccountComponent],
      providers: [
        { provide: DOCUMENT, useValue: mockDocument }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DeleteAccountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should detect LTR direction by default', () => {
    expect(component.isRTL).toBeFalse();
    expect(component.getCurrentDirection()).toBe('ltr');
  });

  it('should detect RTL when dir attribute is rtl', () => {
    mockDocument.documentElement.getAttribute.and.returnValue('rtl');
    component['detectRTL']();
    expect(component.isRTL).toBeTrue();
    expect(component.getCurrentDirection()).toBe('rtl');
  });

  it('should detect RTL when lang is ar', () => {
    mockDocument.documentElement.getAttribute.and.callFake((attr: string) => {
      if (attr === 'lang') return 'ar';
      return 'ltr';
    });
    component['detectRTL']();
    expect(component.isRTL).toBeTrue();
    expect(component.getCurrentDirection()).toBe('rtl');
  });

  it('should detect RTL when computed style direction is rtl', () => {
    (window.getComputedStyle as jasmine.Spy).and.returnValue({
      direction: 'rtl'
    } as CSSStyleDeclaration);
    component['detectRTL']();
    expect(component.isRTL).toBeTrue();
    expect(component.getCurrentDirection()).toBe('rtl');
  });

  it('should start at step 1', () => {
    expect(component.currentStep).toBe(1);
  });

  it('should initialize with empty form values', () => {
    expect(component.email).toBe('');
    expect(component.otpCode).toBe('');
    expect(component.confirmDeletion).toBeFalse();
    expect(component.isLoading).toBeFalse();
    expect(component.errorMessage).toBe('');
    expect(component.resendCooldown).toBe(0);
  });

  it('should progress to step 2 after sending OTP', async () => {
    component.email = '<EMAIL>';
    await component.sendOTP();
    expect(component.currentStep).toBe(2);
  });

  it('should progress to step 3 after verifying OTP', async () => {
    component.otpCode = '123456';
    await component.verifyOTP();
    expect(component.currentStep).toBe(3);
  });

  it('should progress to step 4 after deleting account', async () => {
    component.confirmDeletion = true;
    await component.deleteAccount();
    expect(component.currentStep).toBe(4);
  });

  it('should go back to previous step', () => {
    component.currentStep = 3;
    component.goBack();
    expect(component.currentStep).toBe(2);
  });

  it('should not go back from step 1', () => {
    component.currentStep = 1;
    component.goBack();
    expect(component.currentStep).toBe(1);
  });

  it('should clean up mutation observer on destroy', () => {
    const disconnectSpy = jasmine.createSpy('disconnect');
    component['mutationObserver'] = { disconnect: disconnectSpy } as any;
    component.ngOnDestroy();
    expect(disconnectSpy).toHaveBeenCalled();
  });
});
