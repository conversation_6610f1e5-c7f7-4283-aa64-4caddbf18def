{"common": {"AddToLandingPage": "Add to Landing Page", "dashboard": "Dashboard", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "search": "Search", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "loading": "Loading...", "noData": "No Data", "error": "An unexpected error occurred. Please try again.", "success": "Success", "warning": "Warning", "info": "Information", "previous": "Previous", "next": "Next", "first": "First", "last": "Last", "show": "Show", "entries": "Entries", "showing": "Showing", "to": "to", "of": "of", "page": "Page", "firstPage": "First Page", "previousPage": "Previous Page", "nextPage": "Next Page", "lastPage": "Last Page", "firstPageTooltip": "First Page", "previousPageTooltip": "Previous Page", "nextPageTooltip": "Next Page", "lastPageTooltip": "Last Page", "allGovernorates": "All Governorates", "actions": "Actions", "preview": "Preview", "remove": "Remove", "close": "Close", "confirm": "Confirm", "reset": "Reset", "uploadIcon": "Upload Icon", "update": "Update", "add": "Add", "back": "Back", "finish": "Finish", "retry": "Retry", "account": "Account", "toggleSidebar": "Toggle sidebar navigation", "openUserMenu": "Open user menu", "userAvatar": "User avatar", "switchLanguage": "Switch Language", "openThemePicker": "Open theme picker", "messages": {"updates": "Updates", "messages": "Messages", "tasks": "Tasks", "comments": "Comments"}, "loadingMore": "Loading more...", "allNotificationsLoaded": "All notifications loaded", "notifications": "Notifications", "markAllRead": "<PERSON>", "noNotifications": "No notifications", "viewAll": "View All", "notificationTypes": {"newRequest": "New Request", "requestAccepted": "Request Accepted", "requestRejected": "Request Rejected", "requestCancelled": "Request Cancelled", "requestCompleted": "Request Completed", "newReservation": "New Reservation"}, "clear": "Clear", "refresh": "Refresh", "view": "View", "yes": "Yes", "no": "No", "unknown": "Unknown", "status": {"cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "ssdUsage": "SSD Usage", "processes": "348 Processes. 1/4 Cores.", "memory": "11444GB/16384MB", "storage": "243GB/256GB"}, "tasks": {"upgradeNpm": "Upgrade NPM", "reactVersion": "ReactJS Version", "vueVersion": "VueJS Version", "newLayouts": "Add new layouts", "angularVersion": "Angular Version"}, "time": {"42": "42"}, "colors": "Colors", "typography": "Typography", "components": "Components", "base": "Base", "accordion": "Accordion", "breadcrumbs": "Breadcrumbs", "cards": "Cards", "carousel": "Carousel", "collapse": "Collapse", "listGroup": "List Group", "navsTabs": "Navs & Tabs", "pagination": "Pagination", "placeholder": "Placeholder", "popovers": "Popovers", "progress": "Progress", "spinners": "Spinners", "tables": "Tables", "tabs": "Tabs", "tooltips": "Tooltips", "buttons": "Buttons", "buttonGroups": "Button groups", "dropdowns": "Dropdowns", "forms": "Forms", "formControl": "Form Control", "select": "Select", "checksRadios": "Checks & Radios", "range": "Range", "inputGroup": "Input Group", "floatingLabels": "Floating Labels", "layout": "Layout", "validation": "Validation", "charts": "Charts", "icons": "Icons", "coreuiFree": "CoreUI Free", "coreuiFlags": "CoreUI Flags", "coreuiBrands": "CoreUI Brands", "alerts": "<PERSON><PERSON><PERSON>", "badges": "Badges", "modal": "Modal", "toast": "Toast", "widgets": "Widgets", "extras": "Extras", "pages": "Pages", "error404": "Error 404", "error500": "Error 500", "links": "Links", "docs": "Docs", "confirmDelete": "Are you sure you want to delete this item?", "name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "active": "Active", "inactive": "Inactive"}, "terms": {"title": "Terms and Conditions", "intro": "Welcome to ProCare, a mobile application for home nursing and medical services. By using the app, you agree to the following terms and conditions:", "acceptance": {"title": "Acceptance of Terms", "content": "By using ProCare, you explicitly agree to be bound by these terms. If you do not agree, please discontinue use immediately."}, "dataCollection": {"title": "Data Collection and Storage", "content": "We collect and store your personal and medical data, including but not limited to:", "points": ["Full name", "Gender", "Date of birth", "Address", "Contact information", "Medical history and health record (chronic conditions, surgeries, medications, allergies, etc.)"], "conclusion": "Your data is used solely to provide proper healthcare services and will not be shared with third parties without your consent unless required by law."}, "privacy": {"title": "Privacy and Confidentiality", "content": "We are committed to protecting your personal and medical data with high standards of confidentiality and security."}, "cookies": {"title": "Use of Cookies", "content": "ProCare uses cookies to enhance and personalize your experience. By using the app, you consent to our use of cookies in accordance with our policy. You may disable cookies through your device settings, but doing so may affect some app functionalities."}, "intellectualProperty": {"title": "Intellectual Property Rights", "content": "All content, trademarks, designs, and code within ProCare are owned by the company. Reuse or reproduction is prohibited without prior written permission."}, "disclaimer": {"title": "Disclaimer of Liability", "points": ["The app is provided \"as is\" without warranties.", "We are not liable for unexpected medical outcomes.", "You are responsible for how you use the services."]}, "changes": {"title": "Changes to Terms", "content": "ProCare may update these terms at any time. Users will be notified via the app or email. Continued use of the app means acceptance of the updated terms."}, "governingLaw": {"title": "Governing Law", "content": "These terms are governed by the laws of the Arab Republic of Egypt."}}, "profile": {"manageYourProfile": "Manage your personal information and account settings", "personalInformation": "Personal Information", "accountInfo": "Account Information", "accountStatus": "Account Status", "memberSince": "Member Since", "lastLogin": "Last Login", "statistics": "Statistics", "totalRequests": "Total Requests", "completedRequests": "Completed Requests", "pendingRequests": "Pending Requests", "totalReservations": "Total Reservations", "changePhoto": "Change Photo", "firstName": "First Name", "lastName": "Last Name", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "cancelConfirmation": "Are you sure you want to cancel? All unsaved changes will be lost.", "saveConfirmation": "Are you sure you want to save these changes?", "profileUpdated": "Profile updated successfully", "profileUpdateError": "Failed to update profile. Please try again.", "imageUploadError": "Failed to upload image. Please try again.", "invalidImageType": "Please select a valid image file (JPG, PNG, GIF)", "imageTooLarge": "Image size must be less than 5MB", "nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "phoneInvalid": "Please enter a valid phone number"}, "notifications": {"manageAllNotifications": "Manage all your notifications", "unread": "unread", "searchPlaceholder": "Search notifications...", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "noNotificationsFound": "No notifications found", "tryDifferentFilters": "Try adjusting your filters or search terms", "noNotificationsYet": "You don't have any notifications yet", "clearFilters": "Clear Filters", "showingResults": "Showing", "pageSize": "Page size", "new": "New", "markRead": "<PERSON> <PERSON>", "read": "Read", "allTypes": "All Types", "allStatus": "All Status"}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "auth": {"login": "Sign In", "signIn": "Sign In", "signUp": "Sign Up", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginError": "Login Failed", "secureLogin": "<PERSON><PERSON>", "secureConnection": "Secure Connection", "dataProtection": "Data Protection", "invalidCredentials": "Phone number or password is wrong", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "signInMessage": "Sign In to your account", "signUpMessage": "Create a new account to get started with our platform. Join us today and experience the benefits of our services.", "registerNow": "Register Now!"}, "nurse": {"patient": "Patient"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {0} characters", "maxLength": "Maximum length is {0} characters", "egyptPhone": "Please enter a valid Egyptian phone number (e.g., ***********)", "phoneError": "Phone Number Error", "passwordMinLength": "Password must be at least 6 characters long", "phoneRequired": "Phone number is required", "passwordRequired": "Password is required"}, "admin": {"title": "Admins Management", "searchPlaceholder": "Search by name or phone number", "search": "Search", "create": "Create Admin", "name": "Name", "phone": "Phone Number", "image": "Image", "actions": "Actions", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this admin?", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "confirmPassword": "Confirm Password", "imageUpload": "Upload Image", "noImage": "No Image", "noResults": "No admins found", "tryDifferentSearch": "Try different search", "editTooltip": "Edit Admin", "deleteTooltip": "Delete Admin"}, "location": {"title": "Location Management", "governorates": "Governorates", "cities": "Cities", "addGovernorate": "Add Governorate", "addCity": "Add City", "editGovernorate": "Edit Governorate", "editCity": "Edit City", "updateGovernorate": "Update Governorate", "updateCity": "Update City", "governorateName": "Governorate Name", "cityName": "City Name", "selectGovernorate": "Select Governorate", "noGovernorates": "No governorates found", "noCities": "No cities found for this governorate", "loading": "Loading...", "error": "Error loading data", "deleteGovernorateConfirm": "Are you sure you want to delete this governorate?", "deleteCityConfirm": "Are you sure you want to delete this city?", "deleteGovernorateSuccess": "Governorate deleted successfully", "deleteCitySuccess": "City deleted successfully", "deleteGovernorateError": "Error deleting governorate", "deleteCityError": "Error deleting city", "selectGovernorateFirst": "Please select a governorate first", "addCityToStart": "Add New City", "selectGovernorateInstruction": "Select a governorate to add a city", "allGovernorates": "All Governorates"}, "disease": {"title": "Disease Management", "searchPlaceholder": "Search by disease name", "create": "Add Disease", "edit": "Edit Disease", "nameEn": "English Name", "nameAr": "Arabic Name", "nameEnPlaceholder": "Enter disease name in English", "nameArPlaceholder": "Enter disease name in Arabic", "confirmDelete": "Are you sure you want to delete this disease?", "noResults": "No diseases found", "tryDifferentSearch": "Try a different search term", "deleteSuccess": "Disease deleted successfully", "addSuccess": "Disease added successfully", "updateSuccess": "Disease updated successfully"}, "serviceCategory": {"title": "Service Categories Management", "noSubCategoriesDescription": "No sub-categories available", "description": "Manage and organize your service categories effectively", "add": "Add New Category", "edit": "Edit Category", "delete": "Delete Category", "nameAr": "Category Name (Arabic)", "nameEn": "Category Name (English)", "descriptionAr": "Category Description (Arabic)", "descriptionEn": "Category Description (English)", "icon": "Category Icon", "actions": "Available Actions", "confirmDelete": "Are you sure you want to delete this category?", "noData": "No service categories available", "empty": "No Service Categories", "emptyDescription": "Start by adding new service categories to organize your services", "addFirst": "Add First Category", "addSubCategory": "Add Sub-Category", "nameArPlaceholder": "Enter category name in Arabic", "nameEnPlaceholder": "Enter category name in English", "descriptionArPlaceholder": "Enter category description in Arabic", "descriptionEnPlaceholder": "Enter category description in English", "subCategories": "Sub-Categories", "deleteTitle": "Delete Category", "deleteConfirmation": "This category and all its associated sub-categories will be deleted. Are you sure you want to proceed?", "loading": "Loading categories...", "error": "Error loading categories", "success": "Category saved successfully", "errorSaving": "Error saving category", "errorDeleting": "Error deleting category", "successDeleting": "Category deleted successfully", "searchPlaceholder": "Search categories...", "noResults": "No results found", "parentCategory": "Parent Category", "selectParentCategory": "Select Parent Category", "noParentCategory": "No parent category", "iconUpload": "Upload Icon", "iconPreview": "Preview Icon", "iconRemove": "Remove Icon", "iconRequired": "Icon is required", "nameRequired": "Category name is required", "descriptionRequired": "Category description is required", "maxLength": "Maximum length is {0} characters", "minLength": "Minimum length is {0} characters", "invalidIcon": "Invalid icon format", "iconSize": "Icon size must not exceed {0} KB", "iconTypes": "Allowed icon types: {0}", "save": "Save Category", "editSuccess": "Category updated successfully", "addSuccess": "Category added successfully", "deleteSuccess": "Category deleted successfully", "editError": "Error updating category", "addError": "Error adding category", "deleteError": "Error deleting category", "confirmDeleteSubCategory": "Are you sure you want to delete this sub-category?", "deleteSubCategorySuccess": "Sub-category deleted successfully", "deleteSubCategoryError": "Error deleting sub-category", "addSubCategorySuccess": "Sub-category added successfully", "addSubCategoryError": "Error adding sub-category", "editSubCategorySuccess": "Sub-category updated successfully", "editSubCategoryError": "Error updating sub-category", "viewSubCategories": "View", "noSubCategories": "No sub-categories available", "fromCallCenter": "From Call Center"}, "users": {"title": "Users"}, "specialty": {"title": "Specialties Management", "add": "Add Specialty", "edit": "Edit", "delete": "Delete", "nameAr": "Specialty Name (Arabic)", "nameEn": "Specialty Name (English)", "descriptionAr": "Specialty Description (Arabic)", "descriptionEn": "Specialty Description (English)", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this specialty?", "noData": "No specialties available", "addSuccess": "Specialty added successfully", "editSuccess": "Specialty updated successfully", "deleteSuccess": "Specialty deleted successfully", "addError": "Error adding specialty", "editError": "Error updating specialty", "deleteError": "Error deleting specialty", "nameArRequired": "Specialty name in Arabic is required", "nameEnRequired": "Specialty name in English is required"}, "map": {"instructions": "Click on the map to select a location or drag the marker to adjust position", "showMap": "Show Map", "hideMap": "Hide Map", "latitude": "Latitude", "longitude": "Longitude", "selectLocation": "Select Location", "currentLocation": "Use Current Location", "locationSelected": "Location Selected", "searchLocation": "Search Location"}, "requests": {"title": "Requests Management", "from": "From", "to": "To", "filter": "Filter", "current": "Current", "previous": "Previous", "loading": "Loading...", "total": "Total Requests", "nurse": "Nurse", "phone": "Phone", "status": "Status", "speciality": "Speciality", "totalPrice": "Total Price", "date": "Date", "details": "Details", "detailsTitle": "Request Details", "latitude": "Latitude", "longitude": "Longitude", "nurseLatitude": "Nurse Latitude", "nurseLongitude": "Nurse Longitude", "noRequests": "No requests found.", "prev": "Prev", "next": "Next", "page": "Page", "locationDetails": "Location Details", "distance": "Distance", "distanceKm": "Distance in Kilometers", "distanceBetween": "Distance between {0} and {1}", "distanceToNurse": "Distance to Nurse", "distanceToUser": "Distance to User", "loadingDetails": "Loading details...", "confirmDelete": "Are you sure you want to delete this request?", "delete": "Delete", "edit": "Edit", "view": "View", "cancel": "Cancel", "confirm": "Confirm", "close": "Close"}, "REPORTS": {"TITLE": "Medical Reports", "SUBTITLE": "Manage patient medical reports and health records", "FILTERS": {"FROM_DATE": "From Date", "TO_DATE": "To Date"}, "BUTTONS": {"ADD_REPORT": "Add Report", "SEARCH": "Search", "RESET": "Reset", "VIEW_DETAILS": "View Details", "PATIENT_REPORTS": "Patient Reports"}, "TABLE": {"TITLE": "Reports List", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "CREATED_AT": "Created Date", "DISEASES": "Diseases", "ACTIONS": "Actions"}, "DETAILS": {"TITLE": "Report Details", "SUBTITLE": "Detailed medical report information", "PATIENT_INFO": "Patient Information", "NURSE_INFO": "Nurse Information", "MEDICAL_INFO": "Medical Information", "DATE_INFO": "Date Information", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "NURSE_PHONE": "Nurse Phone", "DRUGS": "Medications", "DISEASES": "Diseases", "NOTES": "Notes", "CREATED_DATE": "Created Date", "CREATED_TIME": "Created Time"}, "NO_DATA": {"TITLE": "No Reports Found", "MESSAGE": "No medical reports available. Add a new report to get started."}, "PATIENT_REPORTS": {"TITLE": "Patient Medical Reports", "SUBTITLE": "All medical reports for patient", "FOUND_REPORTS": "Found Reports", "ADD_NEW_REPORT": "Add New Report", "NO_REPORTS": {"TITLE": "No Medical Reports Found", "MESSAGE": "No medical reports have been created for patient", "SUGGESTIONS": {"TITLE": "What you can do:", "ITEM1": "Create a new medical report for this patient", "ITEM2": "Check if the patient has reports under a different name", "ITEM3": "Contact the medical team for more information"}, "ADD_REPORT": "Create First Report"}}}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "approved": "Approved", "rejected": "Rejected"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "warning": "Warning", "info": "Information", "deleteSuccess": "Item deleted successfully", "updateSuccess": "Item updated successfully", "addSuccess": "Item added successfully", "deleteError": "Error deleting item", "updateError": "Error updating item", "addError": "Error adding item", "noData": "No data available", "noResults": "No results found"}, "PHARMACY": {"TITLE": "Pharmacies", "ADD_NEW": "Add New Pharmacy", "ADD_TITLE": "Add New Pharmacy", "EDIT_TITLE": "Edit Pharmacy", "TABLE": {"NAME": "Name", "ADDRESS": "Address", "PHONE": "Phone", "EMAIL": "Email", "GOVERNORATE": "Governorate", "CITY": "City", "ACTIONS": "Actions"}, "FORM": {"NAME": "Pharmacy Name", "NAME_PLACEHOLDER": "Enter pharmacy name", "NAME_REQUIRED": "Pharmacy name is required", "ADDRESS": "Address", "ADDRESS_PLACEHOLDER": "Enter pharmacy address", "ADDRESS_REQUIRED": "Address is required", "PHONE": "Phone Number", "PHONE_PLACEHOLDER": "Enter phone number", "PHONE_REQUIRED": "Valid phone number is required", "EMAIL": "Email Address", "EMAIL_PLACEHOLDER": "Enter email address", "EMAIL_INVALID": "Please enter a valid email address", "GOVERNORATE": "Governorate", "GOVERNORATE_PLACEHOLDER": "Select Governorate", "CITY": "City", "CITY_PLACEHOLDER": "Select City", "LOCATION_SECTION": "Location Information", "LATITUDE": "Latitude", "LATITUDE_PLACEHOLDER": "Select location on map", "LONGITUDE": "Longitude", "LONGITUDE_PLACEHOLDER": "Select location on map", "NOTES": "Notes", "NOTES_PLACEHOLDER": "Enter additional notes", "ADDRESS_NOTES": "Address Notes", "ADDRESS_NOTES_PLACEHOLDER": "Enter address details", "VALIDATION_ERROR": "Please fill in all required fields correctly"}, "MAP": {"TITLE": "Select Pharmacy Location", "SELECT_LOCATION": "Select Location on Map", "HIDE_MAP": "Hide Map", "USE_CURRENT_LOCATION": "Use Current Location", "INSTRUCTIONS": "Click on the map to set pharmacy location", "DETAILED_INSTRUCTIONS": "Click anywhere on the map to set the pharmacy location. You can also drag the marker to fine-tune the position.", "LOCATION_ERROR": "Unable to get current location. Please select manually on the map.", "GEOLOCATION_NOT_SUPPORTED": "Geolocation is not supported by this browser."}, "BUTTONS": {"EDIT": "Edit", "DELETE": "Delete", "CANCEL": "Cancel", "ADD": "Add Pharmacy", "UPDATE": "Update Pharmacy"}, "DELETE": {"TITLE": "Delete Pharmacy", "MESSAGE": "Are you sure you want to delete this pharmacy?"}, "MESSAGES": {"ADD_SUCCESS": "Pharmacy added successfully", "UPDATE_SUCCESS": "Pharmacy updated successfully", "DELETE_SUCCESS": "Pharmacy deleted successfully", "DELETE_CONFIRM": "Are you sure you want to delete this pharmacy?", "ERROR": {"LOAD_PHARMACIES": "Error loading pharmacies", "LOAD_GOVERNORATES": "Error loading governorates", "LOAD_CITIES": "Error loading cities", "ADD": "Error adding pharmacy", "UPDATE": "Error updating pharmacy", "DELETE": "Error deleting pharmacy"}}}, "RESERVATION": {"TITLE": "Reservations Management", "SUBTITLE": "Manage patient reservations and appointments", "FILTERS": {"TITLE": "Search & Filter", "SEARCH": "Search", "SEARCH_PLACEHOLDER": "Search by patient name or phone", "STATUS": "Status", "ALL_STATUS": "All Status", "FROM_DATE": "From Date", "TO_DATE": "To Date"}, "TABLE": {"TITLE": "Reservations List", "ID": "ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone", "DATE": "Date", "TIME": "Time", "STATUS": "Status", "LOCATION": "Location", "ACTIONS": "Actions"}, "STATUS": {"NEW": "New", "IN_PROGRESS": "In Progress", "COMPLETED": "Completed", "CANCELLED": "Cancelled"}, "BUTTONS": {"SEARCH": "Search", "RESET": "Reset", "CLEAR": "Clear", "VIEW_DETAILS": "View Details", "COMPLETE": "Complete", "CANCEL": "Cancel", "DELETE": "Delete", "PRINT": "Print"}, "DETAILS": {"TITLE": "Reservation Details", "SUBTITLE": "Detailed information for reservation", "ID": "Reservation ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone Number", "DATE": "Appointment Date", "TIME": "Appointment Time", "STATUS": "Status", "GOVERNORATE": "Governorate", "CITY": "City", "ADDRESS_NOTES": "Address Notes", "NOTES": "Notes", "PATIENT_INFO": "Patient Information", "APPOINTMENT_INFO": "Appointment Information", "LOCATION_INFO": "Location Information", "ADDITIONAL_INFO": "Additional Information"}, "CONFIRM": {"TITLE": "Confirm Action", "COMPLETE_TITLE": "Complete Reservation", "COMPLETE_MESSAGE": "Are you sure you want to mark this reservation as completed?", "CANCEL_TITLE": "Cancel Reservation", "CANCEL_MESSAGE": "Are you sure you want to cancel this reservation?", "DELETE_TITLE": "Delete Reservation", "DELETE_MESSAGE": "Are you sure you want to delete this reservation? This action cannot be undone."}, "MESSAGES": {"COMPLETED_SUCCESS": "Reservation completed successfully", "CANCELLED_SUCCESS": "Reservation cancelled successfully", "DELETED_SUCCESS": "Reservation deleted successfully", "LOAD_ERROR": "Error loading reservations", "ACTION_ERROR": "Error performing action"}, "NO_DATA": {"TITLE": "No Reservations Found", "MESSAGE": "No reservations match your current filters. Try adjusting your search criteria."}}, "landing": {"hero_title": "Healthcare at Your Doorstep", "hero_subtitle": "Experience professional healthcare services from the comfort of your home", "features": "Features", "features_title": "Our Features", "how_it_works": "How It Works", "how_it_works_title": "How It Works", "why_choose": "Why Choose Us", "why_choose_title": "Why Choose ProCare", "faq": "FAQ", "testimonials_title": "What Our Clients Say", "no_testimonials": "No testimonials available at the moment", "step1_title": "Download the App", "step1_desc": "Get started by downloading the ProCare app from the App Store or Google Play", "step2_title": "Book Your Service", "step2_desc": "Choose from our wide range of healthcare services and book your appointment", "step3_title": "Get Professional Care", "step3_desc": "Receive high-quality healthcare services from our certified professionals", "feature1_title": "Professional Healthcare", "feature1_desc": "Certified medical professionals providing quality care at your doorstep", "feature2_title": "24/7 Availability", "feature2_desc": "Round-the-clock healthcare services whenever you need them", "feature3_title": "Comprehensive Services", "feature3_desc": "Wide range of medical and nursing services for all your healthcare needs", "feature4_title": "Easy Booking", "feature4_desc": "Simple and convenient appointment booking through our mobile app", "benefit1_title": "Trusted & Reliable", "benefit1_desc": "Verified healthcare professionals with proven track records", "benefit2_title": "Secure & Private", "benefit2_desc": "Your health information is protected with advanced security measures", "benefit3_title": "Fast Response", "benefit3_desc": "Quick response times for urgent healthcare needs", "benefit4_title": "Affordable Pricing", "benefit4_desc": "Competitive pricing for high-quality healthcare services", "footer_description": "ProCare provides professional healthcare services at your doorstep. Experience quality care with our certified medical professionals.", "footer_quick_links": "Quick Links", "footer_contact": "Contact Us", "footer_download": "Download App", "footer_phone": "Call Us: **********", "footer_email": "Email: <EMAIL>", "footer_email_hr": "HR: <EMAIL>", "footer_email_support": "Support: <EMAIL>", "footer_address": "Visit Our Location", "footer_rights": "All rights reserved."}, "deleteAccount": {"title": "Delete Account", "subtitle": "Permanently remove your ProCare account and all associated data", "warning": {"title": "Important: This action cannot be undone", "point1": "All your personal data will be permanently deleted", "point2": "Your reservation history will be removed", "point3": "You will lose access to all ProCare services", "point4": "This action cannot be reversed"}, "step1": {"title": "Email Verification", "description": "Enter your registered email address to receive a verification code", "emailLabel": "Email Address", "emailPlaceholder": "Enter your email address", "sendOTP": "Send Verification Code"}, "step2": {"title": "Verify Your Identity", "description": "Enter the 6-digit verification code sent to", "otpLabel": "Verification Code", "otpPlaceholder": "Enter 6-digit code", "verify": "Verify Code and Delete Account", "resend": "Resend Code", "resendIn": "Resend in"}, "success": {"title": "Account Deleted Successfully", "message": "Your ProCare account has been permanently deleted. Thank you for using our services.", "backToHome": "Back to Home"}}}