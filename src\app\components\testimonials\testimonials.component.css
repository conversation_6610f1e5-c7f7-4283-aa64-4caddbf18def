:host {
  --primary-color: var(--primary, #cd2c4e);
  --primary-light: rgba(205, 44, 78, 0.1);
  --primary-shadow: rgba(205, 44, 78, 0.3);
  --gold: #ffd700;
  --white: #fff;
  --dark: #444;
  --gray: #666;
  --light-gray: #888;
}

.testimonials {
  padding: 6rem 0;
  background: linear-gradient(135deg, #FDD5DD 0%, #F8E6E9 50%, #FDD5DD 100%);
  position: relative;
  overflow: hidden;
}

.testimonials::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 25% 25%, rgba(205, 44, 78, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
}

.section-title {
  text-align: center;
  font: 800 2.8rem/1.2 inherit;
  color: var(--primary, #cd2c4e);
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary, #cd2c4e), #E91E63, var(--primary, #cd2c4e));
  border-radius: 2px;
}

/* Loading, Error, Empty States */
.loading-container, .error-container, .empty-container {
  padding: 4rem 2rem;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(205, 44, 78, 0.1);
  border-left: 4px solid var(--primary, #cd2c4e);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.error-message {
  color: #dc3545;
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.retry-btn {
  background: var(--primary, #cd2c4e);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #b8365a;
  transform: translateY(-2px);
}

/* Slider Container */
.testimonials-slider {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 2rem;
  z-index: 2;
}

.slider-wrapper {
  display: flex;
  transition: transform 0.6s ease;
}

.testimonial-slide {
  flex: 0 0 100%;
  min-height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
}

.testimonial-card {
  background: linear-gradient(145deg, white 0%, #fafafa 100%);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  text-align: center;
  width: 100%;
  max-width: 650px;
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-8px);
}

.stars {
  color: #ffd700;
  font-size: 2rem;
  margin-bottom: 2rem;
}

.stars-container {
  display: inline-block;
}

.star.filled {
  color: #ffd700;
}

.star.empty {
  color: #ddd;
}

.testimonial-comment {
  font: italic 1.2rem/1.8 inherit;
  margin-bottom: 2rem;
  color: #444;
  text-align: center;
}

.testimonial-comment::before, .testimonial-comment::after {
  content: '"';
  font: bold 4rem/1 Georgia, serif;
  color: var(--primary, #cd2c4e);
  position: absolute;
  opacity: 0.15;
}

.testimonial-comment::before {
  top: -1rem;
  left: -1rem;
}

.testimonial-comment::after {
  bottom: -2rem;
  right: -1rem;
}

.testimonial-author {
  position: relative;
}

.nurse-name {
  color: var(--primary, #cd2c4e);
  font: 700 1.2rem/1 inherit;
  margin-bottom: 0.5rem;
  display: block;
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.patient-name {
  color: #666;
  font-size: 0.9rem;
}

.testimonial-date {
  color: #888;
  font-size: 0.8rem;
  font-style: italic;
}

/* Navigation */
.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border: 2px solid var(--primary, #cd2c4e);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--primary, #cd2c4e);
  z-index: 10;
  transition: all 0.3s ease;
}

.slider-nav:hover {
  background: var(--primary, #cd2c4e);
  color: white;
}

.slider-nav:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.slider-nav.prev { left: -25px; }
.slider-nav.next { right: -25px; }

.slider-dots {
  display: flex;
  justify-content: center;
  gap: 0.8rem;
  margin-top: 2rem;
}

.slider-counter {
  position: absolute;
  bottom: -40px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  border: 2px solid var(--primary, #cd2c4e);
  font: 600 0.8rem/1 inherit;
  color: var(--primary, #cd2c4e);
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--primary, #cd2c4e);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot:hover {
  transform: scale(1.2);
}

.slider-dot.active {
  background: var(--primary, #cd2c4e);
  transform: scale(1.3);
}

.animate-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.animate-fade-in.in-view {
  opacity: 1;
  transform: translateY(0);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* RTL Support */
[dir="rtl"] .testimonial-comment::before,
[data-direction="rtl"] .testimonial-comment::before { left: auto; right: -1rem; }
[dir="rtl"] .testimonial-comment::after,
[data-direction="rtl"] .testimonial-comment::after { right: auto; left: -1rem; }
[dir="rtl"] .slider-nav.prev,
[data-direction="rtl"] .slider-nav.prev { left: auto; right: -25px; }
[dir="rtl"] .slider-nav.next,
[data-direction="rtl"] .slider-nav.next { right: auto; left: -25px; }
[dir="rtl"] .slider-counter,
[data-direction="rtl"] .slider-counter { right: auto; left: 20px; }
[data-direction="rtl"] .testimonial-comment,
[data-direction="rtl"] .testimonial-author,
[data-direction="rtl"] .section-title { text-align: right; direction: rtl; }
[data-direction="rtl"] .slider-nav svg { transform: scaleX(-1); }

/* Responsive */
@media (max-width: 768px) {
  .testimonials { padding: 4rem 0; }
  .section-title { font-size: 2.2rem; margin-bottom: 3rem; }
  .testimonials-slider { margin: 0 1rem; padding: 0 1rem; }
  .testimonial-card { padding: 2rem; }
  .testimonial-comment { font-size: 1.1rem; }
  .testimonial-comment::before, .testimonial-comment::after { font-size: 3rem; }
  .slider-nav { width: 40px; height: 40px; }
  .slider-nav.prev { left: -20px; }
  .slider-nav.next { right: -20px; }
  .stars { font-size: 1.8rem; }
  .nurse-name { font-size: 1.1rem; }
  .slider-counter { bottom: -30px; font-size: 0.7rem; }
  [dir="rtl"] .slider-nav.prev, [data-direction="rtl"] .slider-nav.prev { right: -20px; left: auto; }
  [dir="rtl"] .slider-nav.next, [data-direction="rtl"] .slider-nav.next { left: -20px; right: auto; }
}

@media (max-width: 480px) {
  .testimonials { padding: 3rem 0; }
  .section-title { font-size: 1.8rem; margin-bottom: 2rem; }
  .slider-nav.prev { left: 5px; }
  .slider-nav.next { right: 5px; }
  .testimonial-card { padding: 1.5rem; }
  .testimonial-comment::before, .testimonial-comment::after { display: none; }
  .stars { font-size: 1.6rem; }
  .slider-dots { margin-top: 1.5rem; gap: 0.6rem; }
  .slider-dot { width: 10px; height: 10px; }
  [dir="rtl"] .slider-nav.prev, [data-direction="rtl"] .slider-nav.prev { right: 5px; left: auto; }
  [dir="rtl"] .slider-nav.next, [data-direction="rtl"] .slider-nav.next { left: 5px; right: auto; }
  [dir="rtl"] .slider-counter, [data-direction="rtl"] .slider-counter { left: 10px; right: auto; }
}

