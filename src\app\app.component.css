 
    .landing-page {
      font-family: 'Cairo', sans-serif;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      position: relative;
      padding-top: 80px; /* Space for fixed header */
    }

    router-outlet {
      flex: 1;
    }

    /* Adjust padding for mobile */
    @media (max-width: 768px) {
      .landing-page {
        padding-top: 70px; /* Smaller space for mobile */
      }
    }

    /* Back to Top Button Styles */
    .back-to-top-btn {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 56px;
      height: 56px;
      background: var(--primary);
      color: white;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 20px rgba(205, 44, 78, 0.3);
      transform: translateY(100px);
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      z-index: 1000;

      /* Pulse animation */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: var(--primary);
        animation: pulse 2s infinite;
        z-index: -1;
      }

      /* Hover effects */
      &:hover {
        transform: translateY(0) scale(1.1);
        box-shadow: 0 6px 25px rgba(205, 44, 78, 0.4);

        &::before {
          animation-play-state: paused;
        }

        svg {
          animation: bounceUp 0.6s ease;
        }
      }

      /* Focus state for accessibility */
      &:focus {
        outline: 3px solid rgba(205, 44, 78, 0.5);
        outline-offset: 2px;
      }

      /* Active state */
      &:active {
        transform: translateY(0) scale(0.95);
      }

      svg {
        transition: all 0.3s ease;
      }
    }

    /* Contact FAB Styles */
    .contact-fab {
      position: fixed;
      bottom: 2rem;
      left: 2rem;
      z-index: 1000;
    }

    .contact-main-btn {
      width: 56px;
      height: 56px;
      background: #25D366; /* WhatsApp green */
      color: white;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;

      /* Pulse animation */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: #25D366;
        animation: pulse 2s infinite;
        z-index: -1;
      }

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(37, 211, 102, 0.4);

        &::before {
          animation-play-state: paused;
        }
      }

      &:focus {
        outline: 3px solid rgba(37, 211, 102, 0.5);
        outline-offset: 2px;
      }

      svg {
        transition: transform 0.3s ease;
      }
    }

    .contact-fab.expanded .contact-main-btn {
      transform: rotate(45deg);
      background: #128C7E; /* Darker green when expanded */
    }

    .contact-options {
      position: absolute;
      bottom: 70px;
      left: 0;
      display: flex;
      flex-direction: column;
      gap: 12px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(20px);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .contact-fab.expanded .contact-options {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .contact-option {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transform: scale(0);
      animation: popIn 0.3s ease forwards;
    }

    .contact-fab.expanded .contact-option:nth-child(1) {
      animation-delay: 0.1s;
    }

    .contact-fab.expanded .contact-option:nth-child(2) {
      animation-delay: 0.2s;
    }

    .contact-fab.expanded .contact-option:nth-child(3) {
      animation-delay: 0.3s;
    }

    .contact-option.whatsapp {
      background: #25D366;

      &:hover {
        background: #128C7E;
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
      }
    }

    .contact-option.email {
      background: #EA4335; /* Gmail red */

      &:hover {
        background: #D33B2C;
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(234, 67, 53, 0.4);
      }
    }

    .contact-option.phone {
      background: #1976D2; /* Material blue */

      &:hover {
        background: #1565C0;
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
      }
    }

    /* Footer styling - when button is on footer */
    .back-to-top-btn.on-footer {
      background: rgba(255, 255, 255, 0.95);
      color: var(--primary);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border: 2px solid rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);

      &::before {
        background: rgba(255, 255, 255, 0.95);
        animation: pulseLight 2s infinite;
      }

      &:hover {
        background: white;
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        transform: translateY(0) scale(1.1);

        &::before {
          animation-play-state: paused;
        }
      }

      &:focus {
        outline: 3px solid rgba(255, 255, 255, 0.8);
        outline-offset: 2px;
      }
    }

    /* Visible state */
    .back-to-top-btn.visible {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }

    /* Pulse animation keyframes */
    @keyframes pulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.15);
        opacity: 0.7;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Light pulse for footer */
    @keyframes pulseLight {
      0% {
        transform: scale(1);
        opacity: 0.9;
      }
      50% {
        transform: scale(1.15);
        opacity: 0.6;
      }
      100% {
        transform: scale(1);
        opacity: 0.9;
      }
    }

    /* Bounce up animation for arrow */
    @keyframes bounceUp {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-4px);
      }
      60% {
        transform: translateY(-2px);
      }
    }

    /* Pop in animation for contact options */
    @keyframes popIn {
      0% {
        transform: scale(0);
        opacity: 0;
      }
      50% {
        transform: scale(1.2);
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .back-to-top-btn {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 48px;
        height: 48px;
      }

      .contact-fab {
        bottom: 1.5rem;
        left: 1.5rem;
      }

      .contact-main-btn {
        width: 48px;
        height: 48px;
      }

      .contact-option {
        width: 44px;
        height: 44px;
      }

      .contact-options {
        bottom: 60px;
      }
    }

    /* Small mobile adjustments */
    @media (max-width: 480px) {
      .contact-fab {
        bottom: 1rem;
        left: 1rem;
      }

      .back-to-top-btn {
        bottom: 1rem;
        right: 1rem;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .back-to-top-btn {
        box-shadow: 0 4px 20px rgba(205, 44, 78, 0.4);

        &:hover {
          box-shadow: 0 6px 25px rgba(205, 44, 78, 0.5);
        }

        &.on-footer {
          background: rgba(40, 40, 40, 0.95);
          color: white;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          border: 2px solid rgba(255, 255, 255, 0.2);

          &:hover {
            background: rgba(60, 60, 60, 0.95);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
          }
        }
      }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
      .back-to-top-btn {
        transition: opacity 0.3s ease, visibility 0.3s ease;

        &::before {
          animation: none;
        }

        &:hover svg {
          animation: none;
        }
      }

      @keyframes pulse {
        /* Remove pulse animation for users who prefer reduced motion */
      }

      @keyframes pulseLight {
        /* Remove pulse animation for users who prefer reduced motion */
      }
    }

